#!/usr/bin/env python3
"""
测试仿真输出功能
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simulation_output():
    """测试仿真运行和结果输出"""
    print("=" * 60)
    print("CMOST 仿真输出测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        from cmost.utils.file_io import save_simulation_results
        
        print("✓ 模块导入成功")
        
        # 创建设置对象
        print("\n1. 创建仿真设置...")
        settings = Settings()
        
        # 设置基本参数
        settings.set('Number_patients', 1000)  # 使用较小的患者数量进行测试
        settings.set('Simulation_years', 10)   # 10年仿真期
        settings.set('start_age', 20)
        settings.set('end_age', 85)
        settings.set('screening_start_age', 50)
        settings.set('screening_end_age', 75)
        settings.set('screening_interval', 10)
        settings.set('enable_screening', True)
        settings.set('random_seed', 12345)
        
        print("✓ 仿真参数设置完成")
        print(f"  - 患者数量: {settings.get('Number_patients')}")
        print(f"  - 仿真年数: {settings.get('Simulation_years')}")
        print(f"  - 随机种子: {settings.get('random_seed')}")
        
        # 创建仿真对象
        print("\n2. 创建仿真对象...")
        simulation = Simulation(settings)
        print("✓ 仿真对象创建成功")
        
        # 初始化人群
        print("\n3. 初始化人群...")
        simulation.initialize_population()
        print(f"✓ 人群初始化完成，共 {len(simulation.patients)} 个患者")
        
        # 运行仿真
        print("\n4. 运行仿真...")
        start_time = time.time()
        
        # 运行仿真（简化版本）
        results = simulation.run(years=settings.get('Simulation_years'))
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✓ 仿真运行完成，耗时 {duration:.2f} 秒")
        
        # 获取统计结果
        print("\n5. 生成统计结果...")
        stats = simulation.get_summary_statistics()
        print("✓ 统计结果生成完成")
        
        # 显示主要结果
        print("\n6. 主要结果:")
        print("-" * 40)
        if isinstance(stats, dict):
            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value}")
                elif isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            print(f"    {sub_key}: {sub_value}")
        
        # 创建结果目录
        print("\n7. 保存结果...")
        results_dir = "results"
        os.makedirs(results_dir, exist_ok=True)
        
        # 准备结果数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_data = {
            'timestamp': timestamp,
            'settings': {
                'Number_patients': settings.get('Number_patients'),
                'Simulation_years': settings.get('Simulation_years'),
                'random_seed': settings.get('random_seed'),
                'screening_enabled': settings.get('enable_screening')
            },
            'statistics': stats,
            'simulation_info': {
                'duration_seconds': duration,
                'total_patients': len(simulation.patients),
                'completed_successfully': True
            }
        }
        
        # 保存为JSON格式
        json_file = os.path.join(results_dir, f"simulation_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 结果已保存到: {json_file}")
        
        # 使用CMOST的文件IO功能保存
        cmost_file = os.path.join(results_dir, f"cmost_results_{timestamp}.json")
        save_simulation_results(result_data, cmost_file, 'json')
        print(f"✓ 结果已保存到: {cmost_file}")
        
        # 保存简化的Excel格式（如果可能）
        try:
            import pandas as pd
            
            # 创建简化的数据框
            summary_data = {
                'Metric': [],
                'Value': []
            }
            
            if isinstance(stats, dict):
                for key, value in stats.items():
                    if isinstance(value, (int, float)):
                        summary_data['Metric'].append(key)
                        summary_data['Value'].append(value)
            
            df = pd.DataFrame(summary_data)
            excel_file = os.path.join(results_dir, f"simulation_summary_{timestamp}.xlsx")
            df.to_excel(excel_file, index=False)
            print(f"✓ 摘要已保存到: {excel_file}")
            
        except ImportError:
            print("⚠ 无法保存Excel格式（需要安装pandas和openpyxl）")
        
        # 验证文件是否存在
        print("\n8. 验证输出文件...")
        for file_path in [json_file, cmost_file]:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✓ {file_path} (大小: {file_size} 字节)")
            else:
                print(f"✗ {file_path} 不存在")
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("✅ 仿真运行成功")
        print("✅ 结果输出正常")
        print("✅ 文件保存成功")
        print(f"\n📁 结果文件保存在: {os.path.abspath(results_dir)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simulation_output()
    sys.exit(0 if success else 1)
