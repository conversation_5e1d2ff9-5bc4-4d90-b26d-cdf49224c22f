#!/usr/bin/env python3
"""
测试增强UI的启动问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试所有必要的导入"""
    print("=" * 60)
    print("测试CMOST增强UI导入")
    print("=" * 60)
    
    try:
        print("1. 测试基础模块导入...")
        from cmost.config.settings import Settings
        print("✓ Settings 导入成功")
        
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        print("✓ EnhancedMainWindow 导入成功")
        
        print("\n2. 测试可选模块导入...")
        try:
            from cmost.calibration.integrated_calibration import IntegratedCalibrator
            print("✓ IntegratedCalibrator 导入成功")
        except ImportError as e:
            print(f"⚠ IntegratedCalibrator 导入失败: {e}")
        
        try:
            from cmost.screening.strategy_manager import ScreeningStrategyManager
            print("✓ ScreeningStrategyManager 导入成功")
        except ImportError as e:
            print(f"⚠ ScreeningStrategyManager 导入失败: {e}")
        
        try:
            from cmost.ui.calibration_panel import CalibrationPanel
            print("✓ CalibrationPanel 导入成功")
        except ImportError as e:
            print(f"⚠ CalibrationPanel 导入失败: {e}")
        
        try:
            from cmost.ui.screening_config_panel import ScreeningConfigPanel
            print("✓ ScreeningConfigPanel 导入成功")
        except ImportError as e:
            print(f"⚠ ScreeningConfigPanel 导入失败: {e}")
        
        try:
            from cmost.ui.output_config_panel import OutputConfigPanel
            print("✓ OutputConfigPanel 导入成功")
        except ImportError as e:
            print(f"⚠ OutputConfigPanel 导入失败: {e}")
        
        print("\n✅ 基础导入测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_ui_creation():
    """测试UI创建"""
    print("\n" + "=" * 60)
    print("测试UI创建")
    print("=" * 60)
    
    try:
        print("1. 创建Tkinter根窗口...")
        root = tk.Tk()
        root.title("CMOST UI 测试")
        root.geometry("800x600")
        print("✓ 根窗口创建成功")
        
        print("2. 创建EnhancedMainWindow...")
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        
        # 在这里捕获任何初始化错误
        try:
            app = EnhancedMainWindow(root)
            print("✓ EnhancedMainWindow 创建成功")
            
            print("3. 测试窗口显示...")
            root.update()  # 强制更新一次
            print("✓ 窗口更新成功")
            
            # 显示一个简短的消息然后关闭
            def close_after_delay():
                root.after(3000, root.destroy)  # 3秒后关闭
            
            close_after_delay()
            
            print("4. 启动主循环...")
            print("   (窗口将在3秒后自动关闭)")
            root.mainloop()
            
            print("✅ UI测试完成")
            return True
            
        except Exception as e:
            print(f"❌ EnhancedMainWindow 创建失败: {e}")
            traceback.print_exc()
            root.destroy()
            return False
            
    except Exception as e:
        print(f"❌ UI创建测试失败: {e}")
        traceback.print_exc()
        return False

def test_manual_ui():
    """手动测试UI - 需要用户交互"""
    print("\n" + "=" * 60)
    print("手动UI测试")
    print("=" * 60)
    
    try:
        print("创建手动测试窗口...")
        root = tk.Tk()
        root.title("CMOST - 手动测试")
        root.geometry("1200x800")
        
        # 居中显示
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (root.winfo_screenheight() // 2) - (800 // 2)
        root.geometry(f"1200x800+{x}+{y}")
        
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        app = EnhancedMainWindow(root)
        
        print("✅ 手动测试窗口已启动")
        print("请在GUI窗口中进行测试...")
        print("关闭窗口以结束测试")
        
        root.mainloop()
        
        print("✅ 手动测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("CMOST 增强UI 诊断工具")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，无法继续")
        return
    
    # 询问用户要进行哪种测试
    print("\n选择测试模式:")
    print("1. 自动测试 (3秒后自动关闭)")
    print("2. 手动测试 (需要手动关闭)")
    print("3. 跳过UI测试")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            test_ui_creation()
        elif choice == "2":
            test_manual_ui()
        elif choice == "3":
            print("跳过UI测试")
        else:
            print("无效选择，跳过UI测试")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
